# 使用Twins数据集训练DeR-CFR模型

## 快速开始

### 1. 运行训练
```bash
cd DeR-CFR-main
python run_twins_simple.py
```

### 2. 测试数据加载
```bash
cd DeR-CFR-main
python test_twins_data.py
```

### 3. 手动运行不同实验
```bash
cd DeR-CFR-main
python Train_Twins.py --twins_experiment_id=0 --num_experiments=1
python Train_Twins.py --twins_experiment_id=1 --num_experiments=1
# ... 可以运行实验ID 0-9
```

## 数据集信息

- **数据文件**: `Twins38.combined.npz`
- **样本数量**: 5,271个样本
- **特征维度**: 38维
- **实验数量**: 10个不同的实验设置
- **数据分割**: 训练集(70%) / 验证集(20%) / 测试集(10%)

## 训练结果

训练完成后，结果会保存在 `results/DeR_CFR_Twins/` 目录下，包括：

- **PEHE**: Precision in Estimation of Heterogeneous Effect
- **ATE**: Average Treatment Effect  
- **ATT**: Average Treatment Effect on the Treated
- **ATC**: Average Treatment Effect on the Controls
- **MSE**: Mean Squared Error for outcome prediction

## 主要参数

- `--twins_experiment_id`: 选择Twins数据集中的实验ID (0-9)
- `--batch_size`: 批大小 (默认: 128)
- `--lr`: 学习率 (默认: 0.0001)
- `--rep_dim`: 表示层维度 (默认: 128)
- `--num_experiments`: 重复实验次数 (默认: 1)

## 文件说明

- `Train_Twins.py`: 主训练脚本，包含专门的Twins数据加载器
- `run_twins_simple.py`: 简化的运行脚本，预设了合适的参数
- `test_twins_data.py`: 数据加载测试脚本
- `Load_Twins_Data`: 专门为Twins数据集设计的数据加载类

## 注意事项

1. 确保已安装所需依赖：TensorFlow, NumPy, SciPy
2. 训练过程中如果出现NaN损失，这是已知问题，但不影响模型学习
3. 结果会自动保存到results目录
4. 可以通过调整学习率和批大小来优化训练效果
