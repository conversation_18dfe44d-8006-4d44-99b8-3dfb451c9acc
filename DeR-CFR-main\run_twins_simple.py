#!/usr/bin/env python3
"""
简化的Twins数据集训练脚本
"""

import os
import sys

def main():
    """运行Twins数据集训练"""
    
    # 确保results目录存在
    os.makedirs('results', exist_ok=True)
    
    # 设置命令行参数
    sys.argv = [
        'Train_Twins.py',
        '--twins_experiment_id=0',
        '--num_experiments=1',
        '--data_path=Twins38.combined.npz',
        '--output_dir=DeR_CFR_Twins',
        '--y_is_binary=0',
        '--batch_size=128',
        '--lr=0.0001',
        '--seed=888',
        '--rep_dim=128',
        '--rep_layer=2',
        '--t_dim=64',
        '--t_layer=3',
        '--y_dim=64',
        '--y_layer=3'
    ]
    
    # 导入并运行训练函数
    from Train_Twins import train
    
    print("Starting Twins dataset training...")
    print("Experiment ID: 0")
    print("Data path: Twins38.combined.npz")
    print("Output directory: results/DeR_CFR_Twins")
    
    try:
        train()
        print("Training completed successfully!")
    except Exception as e:
        print(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
