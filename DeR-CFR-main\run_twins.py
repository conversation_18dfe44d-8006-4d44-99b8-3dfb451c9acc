#!/usr/bin/env python3
"""
使用Twins数据集运行DeR-CFR模型的脚本
"""

import os
import sys
import subprocess

def run_twins_experiment(experiment_id=0, num_experiments=1):
    """
    运行Twins数据集实验
    
    Args:
        experiment_id: Twins数据集中的实验ID (0-9)
        num_experiments: 重复实验次数
    """
    
    # 确保results目录存在
    os.makedirs('results', exist_ok=True)
    
    # 构建命令行参数
    cmd = [
        'python', 'Train_Twins.py',
        f'--twins_experiment_id={experiment_id}',
        f'--num_experiments={num_experiments}',
        '--data_path=Twins38.combined.npz',
        '--output_dir=DeR_CFR_Twins',
        '--y_is_binary=0',  # Twins数据集通常是连续结果
        '--batch_size=128',  # 适当调整批大小
        '--lr=0.001',
        '--seed=888'
    ]
    
    print(f"Running Twins experiment {experiment_id}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # 运行训练
        result = subprocess.run(cmd, cwd='DeR-CFR-main', capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Experiment {experiment_id} completed successfully!")
            print("Output:", result.stdout)
        else:
            print(f"Experiment {experiment_id} failed!")
            print("Error:", result.stderr)
            
    except Exception as e:
        print(f"Error running experiment: {e}")

def run_all_twins_experiments():
    """运行所有10个Twins实验"""
    for i in range(10):
        print(f"\n{'='*50}")
        print(f"Running Twins Experiment {i}")
        print(f"{'='*50}")
        run_twins_experiment(experiment_id=i, num_experiments=1)

if __name__ == '__main__':
    if len(sys.argv) > 1:
        exp_id = int(sys.argv[1])
        run_twins_experiment(experiment_id=exp_id)
    else:
        # 默认运行实验0
        run_twins_experiment(experiment_id=0)
