from Module import Net
import tensorflow as tf
# 禁用eager execution以兼容TensorFlow 1.x代码
tf.compat.v1.disable_eager_execution()
import utils
import numpy as np
import random
import os
from my_evaluate import evaluation, ACCURACY_func, PEHE_func_v2, PEHE_func_nn, MSE_func
import csv

# hyperparameter (parameter_name, default_value, parameter_description)
FLAGS = tf.compat.v1.app.flags.FLAGS
tf.compat.v1.app.flags.DEFINE_float('p_alpha', 1e-1, "loss-A")
tf.compat.v1.app.flags.DEFINE_float('p_beta', 1, "loss-I")
tf.compat.v1.app.flags.DEFINE_float('p_gamma', 1, "loss-C_B")
tf.compat.v1.app.flags.DEFINE_float('p_mu', 10, "loss-O")
tf.compat.v1.app.flags.DEFINE_float('p_lambda', 1, "los-Reg")
tf.compat.v1.app.flags.DEFINE_float('lr', 0.001, "Learning rate")
tf.compat.v1.app.flags.DEFINE_float('decay_rate', 1.0, "Weight decay rate")
tf.compat.v1.app.flags.DEFINE_integer('seed', 888, "seed")
tf.compat.v1.app.flags.DEFINE_integer('batch_size', 256, "batch_size")
tf.compat.v1.app.flags.DEFINE_integer('num_experiments', 1, "num_experiments")
tf.compat.v1.app.flags.DEFINE_integer('rep_dim', 256, "The dimension of representation network")
tf.compat.v1.app.flags.DEFINE_integer('rep_layer', 2, "The number of representation network layers")
tf.compat.v1.app.flags.DEFINE_integer('t_dim', 128, "The dimension of treatment network")
tf.compat.v1.app.flags.DEFINE_integer('t_layer', 5, "The number of treatment network layers")
tf.compat.v1.app.flags.DEFINE_integer('y_dim', 128, "The dimension of outcome network")
tf.compat.v1.app.flags.DEFINE_integer('y_layer', 5, "The number of outcome network layers")
tf.compat.v1.app.flags.DEFINE_integer('select_layer', 0, "contribution layer")
tf.compat.v1.app.flags.DEFINE_string('activation', 'elu', "Activation function")
tf.compat.v1.app.flags.DEFINE_string('data_path', 'Twins38.combined.npz', "data")
tf.compat.v1.app.flags.DEFINE_string('output_dir', 'DeR_CFR_Twins', "output")
tf.compat.v1.app.flags.DEFINE_string('var_from', 'get_variable', "get_variable/Variable")
tf.compat.v1.app.flags.DEFINE_integer('t_is_binary', 1, "The treatment is binary")
tf.compat.v1.app.flags.DEFINE_integer('y_is_binary', 0, "The outcome is binary")  # Twins数据集通常是连续结果
tf.compat.v1.app.flags.DEFINE_integer('reweight_sample', 1, "sample balance")
tf.compat.v1.app.flags.DEFINE_integer('use_p_correction', 1, "fix coef")
tf.compat.v1.app.flags.DEFINE_integer('batch_norm', 0, "batch normalization")
tf.compat.v1.app.flags.DEFINE_integer('rep_normalization', 0, "representation normalization")
tf.compat.v1.app.flags.DEFINE_integer('twins_experiment_id', 0, "Which experiment to use from Twins dataset (0-9)")

class Load_Twins_Data(object):
    def __init__(self, train_valid_test=[7, 2, 1], seed=123):
        self.data = None
        self.num = 0
        self.rng = np.random.RandomState(seed)
        self.train_valid_test = train_valid_test
    
    def reinit(self):
        self.data = None
        self.num = 0

    def load_twins_npz(self, file_path, ind=0):
        """
        专门为Twins数据集设计的加载函数
        Twins数据集格式: x(5271, 38, 10), t(5271, 10), yf(5271, 10), ycf(5271, 10), mu0(5271, 10), mu1(5271, 10)
        """
        data = np.load(file_path)
        
        # 从指定的实验索引中提取数据
        t = data['t'][:, ind:ind+1]
        y = data['yf'][:, ind:ind+1]
        ycf = data['ycf'][:, ind:ind+1]
        mu0 = data['mu0'][:, ind:ind+1]
        mu1 = data['mu1'][:, ind:ind+1]
        x = data['x'][:, :, ind]  # 提取第ind个实验的特征
        
        self.num = self.num + x.shape[0]
        
        data_list = [x, t, y, ycf, mu0, mu1]
        
        if self.data == None:
            self.data = data_list
        else:
            for i, _ in enumerate(self.data):
                self.data[i] = np.concatenate((self.data[i], data_list[i]), axis=0)

    def split_data(self, train_valid_test=None):
        if train_valid_test == None:
            train_valid_test = (self.num * np.array(self.train_valid_test) / sum(self.train_valid_test)).astype(int)
        else:
            self.train_valid_test = train_valid_test
            train_valid_test = (self.num * np.array(train_valid_test) / sum(train_valid_test)).astype(int)

        self.train = [d[0:train_valid_test[0], :] for d in self.data]
        self.valid = [d[train_valid_test[0]:int(sum(train_valid_test[0:2])), :] for d in self.data]
        self.test = [d[int(sum(train_valid_test[0:2])):, :] for d in self.data]

        self.train_I, self.valid_I, self.test_I = train_valid_test

        self.to_dict()

    def to_dict(self):
        self.train = self.list_2_dict(self.train)
        self.valid = self.list_2_dict(self.valid)
        self.test = self.list_2_dict(self.test)

        self.train['I'] = np.array(range(self.train_I))
        self.valid['I'] = np.array(range(self.valid_I))
        self.test['I'] = np.array(range(self.test_I))

    def list_2_dict(self, data_list):
        data_dict = {}
        data_dict['x'] = data_list[0]
        data_dict['t'] = data_list[1]
        data_dict['y'] = data_list[2]
        data_dict['ycf'] = data_list[3]
        data_dict['mu0'] = data_list[4]
        data_dict['mu1'] = data_list[5]
        return data_dict

    def shuffle(self):
        p = self.rng.permutation(self.num)
        self.data = [d[p] for d in self.data]
        self.split_data()

def run(train, valid, test, output_dir, FLAGS):
    t_threshold = 0.5

    ys = np.concatenate((train['y'], valid['y']), axis=0)
    y_threshold = np.median(ys)

    ''' Output log file '''
    log = utils.Log('results/' + output_dir)

    ''' Set random seed '''
    log.log("Set random seed: {}".format(FLAGS.seed))
    random.seed(FLAGS.seed)
    tf.compat.v1.set_random_seed(FLAGS.seed)
    np.random.seed(FLAGS.seed)

    ''' Session '''
    log.log("Session: Open! ")
    tf.compat.v1.reset_default_graph()
    graph = tf.compat.v1.get_default_graph()
    config = tf.compat.v1.ConfigProto()
    config.gpu_options.allow_growth = True
    sess = tf.compat.v1.Session(graph=graph, config=config)
    log.log("Session: Create！")

    ''' load data '''
    log.log("load data")
    D = [train['x'], train['t'], train['y'], train['I'],
         train['ycf'], train['mu0'], train['mu1']]
    G = utils.batch_G(D, batch_size=FLAGS.batch_size)

    ''' Initialize all parameters '''
    log.log("Initialize all parameters")
    net = Net(train['x'].shape[0], train['x'].shape[1], FLAGS)
    tf.compat.v1.global_variables_initializer().run(session=sess)
    log.log("tf: Complete!")

    ''' dict '''
    log.log("dict")
    train_dict = {net.x: train['x'], net.t: train['t'], net.y: train['y'],
                  net.do_in: 1.0, net.do_out: 1.0, net.p_t: 0.5, net.I: train['I'],
                  net.t_threshold: t_threshold, net.y_threshold: y_threshold}

    valid_dict = {net.x: valid['x'], net.t: valid['t'], net.y: valid['y'],
                  net.do_in: 1.0, net.do_out: 1.0, net.p_t: 0.5, net.I: valid['I'],
                  net.t_threshold: t_threshold, net.y_threshold: y_threshold}
    
    if FLAGS.y_is_binary:
        test_t = test['t']
    else: 
        test_t = test['t'] - test['t']

    test_dict = {net.x: test['x'], net.t: test_t, net.y: test['y'],
                 net.do_in: 1.0, net.do_out: 1.0, net.p_t: 0.5, net.I: test['I'],
                 net.t_threshold: t_threshold, net.y_threshold: y_threshold}
    
    log.log("metric and setting")
    E = evaluation(train, valid, test, 'pehe_nn')
    train_steps = 3000
    num = 1
    if FLAGS.y_is_binary:
        valid_best = 0
        show_best = 0
    else:
        valid_best = 999
        show_best = 999

    log.log("traing ---- ")
    for i in range(train_steps):

        x, t, y, I, yc, mu0, mu1 = G.batch.__next__()

        batch_dict = {net.x: x, net.t: t, net.y: y, net.do_in: 1.0, net.do_out: 1.0, net.p_t: 0.5, net.I: I, net.t_threshold: t_threshold, net.y_threshold: y_threshold}

        sess.run(net.train, feed_dict=batch_dict)
        if FLAGS.reweight_sample:
            sess.run(net.train_balance, feed_dict=batch_dict)

        if i % 100 == 0:
            if FLAGS.y_is_binary:
                pass
            else:
                if i % 500 == 0:
                    loss, y_hat, t_hat = sess.run([net.loss, net.y_pred, net.t_pred_I], feed_dict=batch_dict)
                    valid_y_hat, valid_ycf_hat = sess.run([net.y_pred, net.ycf_pred], feed_dict=valid_dict)
                    test_y0, test_y1, test_t_hat = sess.run([net.y_pred, net.ycf_pred, net.t_pred_I], feed_dict=test_dict)
                    valid_pehe = PEHE_func_nn(valid['x'], valid['t'], valid['y'], valid_y_hat, valid_ycf_hat)
                    pehe = PEHE_func_v2(test['mu1'], test['mu0'], test_y1, test_y0)
                    t_acc = ACCURACY_func(test['t'], test_t_hat)
                    print(np.concatenate((y[:num, :], y_hat[:num, :], t[:num, :], t_hat[:num, :]), axis=1),
                        "{}, loss: {}, Pehe: {}, t_acc: {}".format(i, loss, pehe, t_acc))
                    if valid_best > valid_pehe:
                        valid_best = valid_pehe
                        show_best = pehe
                else:
                    loss, y_hat, t_hat = sess.run([net.loss, net.y_pred, net.t_pred_I], feed_dict=batch_dict)

    log.log("test ---- ")
    test_y0, test_y1, test_t_hat = sess.run([net.y_pred, net.ycf_pred, net.t_pred_I], feed_dict=test_dict)
    
    result = E.figure_out(x=test['x'], t=test['t'], t_pred=test_t_hat, y=test['y'], ycf=test['ycf'], y_pred=test_y1, ycf_pred=test_y0)
    
    log.log("Results:")
    for key, value in result.items():
        log.log(f"{key}: {value}")
    
    sess.close()
    
    return result['pehe'], log, 'pehe'

def train():
    # 使用专门的Twins数据加载器
    D_load = Load_Twins_Data([63, 27, 10])
    
    # 加载Twins数据集
    D_load.load_twins_npz(FLAGS.data_path, FLAGS.twins_experiment_id)
    D_load.split_data()
    
    results = []
    for _ in range(FLAGS.num_experiments):
        D_load.shuffle()
        result, log, ind = run(D_load.train, D_load.valid, D_load.test, FLAGS.output_dir, FLAGS)
        results.append(result)
    
    results = np.array(results)
    
    log.log('{}: {} +/- {}'.format(ind, np.mean(results), np.std(results)))

if __name__ == '__main__':
    train()
