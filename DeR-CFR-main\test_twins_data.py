#!/usr/bin/env python3
"""
测试Twins数据集加载的脚本
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_twins_data_loading():
    """测试Twins数据集的加载"""
    
    print("Testing Twins data loading...")
    
    # 加载原始数据
    data_path = 'Twins38.combined.npz'
    if not os.path.exists(data_path):
        print(f"Error: {data_path} not found!")
        return False
    
    data = np.load(data_path)
    print(f"Original data keys: {list(data.keys())}")
    
    for key in data.keys():
        print(f"{key}: {data[key].shape}")
    
    # 测试我们的数据加载器
    from Train_Twins import Load_Twins_Data
    
    # 测试不同的实验ID
    for exp_id in [0, 1, 2]:
        print(f"\n--- Testing experiment {exp_id} ---")
        
        loader = Load_Twins_Data([7, 2, 1])
        loader.load_twins_npz(data_path, exp_id)
        loader.split_data()
        
        print(f"Total samples: {loader.num}")
        print(f"Train samples: {loader.train['x'].shape[0]}")
        print(f"Valid samples: {loader.valid['x'].shape[0]}")
        print(f"Test samples: {loader.test['x'].shape[0]}")
        print(f"Feature dimensions: {loader.train['x'].shape[1]}")
        
        # 检查数据范围
        print(f"Treatment range: [{loader.train['t'].min():.3f}, {loader.train['t'].max():.3f}]")
        print(f"Outcome range: [{loader.train['y'].min():.3f}, {loader.train['y'].max():.3f}]")
        print(f"Feature range: [{loader.train['x'].min():.3f}, {loader.train['x'].max():.3f}]")
        
        # 检查治疗分配比例
        treatment_ratio = np.mean(loader.train['t'])
        print(f"Treatment ratio: {treatment_ratio:.3f}")
    
    print("\nData loading test completed successfully!")
    return True

def test_data_compatibility():
    """测试数据与模型的兼容性"""
    
    print("\nTesting data compatibility with model...")
    
    try:
        # 导入必要的模块
        import tensorflow as tf
        from Module import Net
        from Train_Twins import Load_Twins_Data
        
        # 加载数据
        loader = Load_Twins_Data([7, 2, 1])
        loader.load_twins_npz('Twins38.combined.npz', 0)
        loader.split_data()
        
        # 创建一个简单的FLAGS对象用于测试
        class TestFlags:
            def __init__(self):
                self.rep_dim = 64
                self.rep_layer = 2
                self.t_dim = 32
                self.t_layer = 3
                self.y_dim = 32
                self.y_layer = 3
                self.activation = 'elu'
                self.t_is_binary = 1
                self.y_is_binary = 0
                self.reweight_sample = 1
                self.use_p_correction = 1
                self.batch_norm = 0
                self.rep_normalization = 0
                self.decay_rate = 1.0
                self.seed = 888
        
        flags = TestFlags()
        
        # 测试网络创建
        tf.compat.v1.reset_default_graph()
        net = Net(loader.train['x'].shape[0], loader.train['x'].shape[1], flags)
        
        print(f"Model created successfully!")
        print(f"Input dimension: {loader.train['x'].shape[1]}")
        print(f"Sample count: {loader.train['x'].shape[0]}")
        
        return True
        
    except Exception as e:
        print(f"Compatibility test failed: {e}")
        return False

if __name__ == '__main__':
    success = test_twins_data_loading()
    if success:
        test_data_compatibility()
    else:
        print("Data loading test failed!")
